services:
  traefik:
    image: "traefik:v3.4" # traefik:v3.5.2
    container_name: "traefik"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    networks:
      - proxy
    command:
      - "--api.insecure=true"
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=proxy"
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--entryPoints.web.address=:80"
      - "--entryPoints.websecure.address=:443"
      - "--entryPoints.websecure.http.tls=true"
      - "--entryPoints.web.http.redirections.entryPoint.to=websecure"
      - "--entryPoints.web.http.redirections.entryPoint.scheme=https"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./certs:/certs:ro"
      - "./dynamic:/etc/traefik/dynamic:ro"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.msarknet.me`)"
      - "traefik.http.routers.dashboard.entrypoints=websecure"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.tls=true"

  # # ===============================
  # # CEREBRO STACK - React Frontend
  # # ===============================
  # cerebro-fe:
  #   build:
  #     context: ../cerebro-fe
  #     dockerfile: Dockerfile
  #     target: development  # Usar stage de desarrollo
  #   container_name: "cerebro-fe"
  #   restart: unless-stopped
  #   networks:
  #     - proxy
  #   volumes:
  #     # Hot reload para desarrollo
  #     - "../cerebro-fe/src:/app/src"
  #     - "../cerebro-fe/public:/app/public"
  #     - "../cerebro-fe/package.json:/app/package.json"
  #     # Evitar conflictos con node_modules
  #     - /app/node_modules
  #   environment:
  #     - NODE_ENV=development
  #     - CHOKIDAR_USEPOLLING=true
  #     - REACT_APP_API_URL=https://api.msarknet.me
  #     - REACT_APP_WS_URL=wss://api.msarknet.me
  #     - GENERATE_SOURCEMAP=true
  #     - FAST_REFRESH=true
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.cerebro-fe.rule=Host(`app.msarknet.me`)"
  #     - "traefik.http.routers.cerebro-fe.entrypoints=websecure"
  #     - "traefik.http.routers.cerebro-fe.tls=true"
  #     - "traefik.http.services.cerebro-fe.loadbalancer.server.port=3000"
  #     # Middleware para SPA (Single Page Application)
  #     - "traefik.http.routers.cerebro-fe.middlewares=spa-headers"

  # ===============================
  # SERVICIOS ORIGINALES
  # ===============================
  main-app:
    image: "nginx:alpine"
    container_name: "main-app"
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      - "./web-content/main:/usr/share/nginx/html:ro"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.main-app.rule=Host(`msarknet.me`)"
      - "traefik.http.routers.main-app.entrypoints=websecure"
      - "traefik.http.routers.main-app.tls=true"

  api-service:
    image: "traefik/whoami"
    container_name: "api-service"
    restart: unless-stopped
    networks:
      - proxy
    environment:
      - WHOAMI_NAME=API Backend Service
    labels:
      - "traefik.enable=true"
      # - "traefik.http.routers.api.rule=Host(`msarknet.me`) && PathPrefix(`/api`)"
      - "traefik.http.routers.api.rule=Host(`whoami.msarknet.me`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      - "traefik.http.routers.api.tls=true"

  grafana-mock:
    image: "grafana/grafana"
    container_name: "grafana-mock"
    restart: unless-stopped
    networks:
      - proxy
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_SECURITY_ADMIN_EMAIL=${GRAFANA_ADMIN_EMAIL}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.msarknet.me`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls=true"

  # loki-service:
  #   image: "grafana/loki"
  #   container_name: "loki-service"
  #   restart: unless-stopped
  #   networks:
  #     - proxy
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.loki.rule=Host(`loki.msarknet.me`)"
  #     - "traefik.http.routers.loki.entrypoints=websecure"
  #     - "traefik.http.routers.loki.tls=true"

  prometheus-mock:
    image: "bitnami/prometheus"
    container_name: "prometheus-mock"
    restart: unless-stopped
    networks:
      - proxy
    environment:
      - WHOAMI_NAME=Prometheus Mock Service
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prom.msarknet.me`)"
      - "traefik.http.routers.prometheus.entrypoints=websecure"
      - "traefik.http.routers.prometheus.tls=true"

  portainer-mock:
    image: "portainer/portainer"
    container_name: "portainer-mock"
    restart: unless-stopped
    networks:
      - proxy
    # volumes:
    #   - "./web-content/portainer:/usr/share/nginx/html:ro"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.portainer.rule=Host(`portainer.msarknet.me`)"
      - "traefik.http.routers.portainer.entrypoints=websecure"
      - "traefik.http.routers.portainer.tls=true"

  docs:
    image: "nginx:alpine"
    container_name: "docs-service"
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      - "./web-content/docs:/usr/share/nginx/html:ro"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docs.rule=Host(`docs.msarknet.me`)"
      - "traefik.http.routers.docs.entrypoints=websecure"
      - "traefik.http.routers.docs.tls=true"

networks:
  proxy:
    driver: bridge

volumes:
  cerebro_db_data:
  cerebro_db_config:

# networks:
#   proxy:
#     name: proxy
#     driver: bridge

# volumes:
#   traefik-acme: {}
#   traefik-logs: {}
#   portainer-data: {}
#   mariadb-data: {}
#   nextcloud-data: {}
#   nextcloud-apps: {}
#   nextcloud-config: {}
#   redis-data: {}
#   elastic-data: {}
#   logstash-pipeline: {}

# services:
#   traefik:
#     image: traefik:v2.11
#     container_name: traefik
#     command:
#       - --api.dashboard=true
#       - --providers.docker=true
#       - --providers.docker.exposedbydefault=false
#       - --entrypoints.web.address=:80
#       - --entrypoints.websecure.address=:443
#       # redirección http -> https
#       - --entrypoints.web.http.redirections.entryPoint.to=websecure
#       - --entrypoints.web.http.redirections.entryPoint.scheme=https
#       # Let's Encrypt (HTTP challenge)
#       - --certificatesresolvers.le.acme.tlschallenge=true
#       - --certificatesresolvers.le.acme.email=${LETSENCRYPT_EMAIL}
#       - --certificatesresolvers.le.acme.storage=/letsencrypt/acme.json
#       # logs
#       - --accesslog=true
#       - --accesslog.filepath=/var/log/traefik/access.log
#       - --log.level=INFO
#     ports:
#       - "80:80"
#       - "443:443"
#     volumes:
#       - traefik-acme:/letsencrypt
#       - traefik-logs:/var/log/traefik
#       - /var/run/docker.sock:/var/run/docker.sock:ro
#     networks:
#       - proxy
#     labels:
#       - traefik.enable=true
#       # dashboard (protege con basic auth si quieres)
#       - traefik.http.routers.traefik.rule=Host(`${TRAEFIK_HOST}`)
#       - traefik.http.routers.traefik.entrypoints=websecure
#       - traefik.http.routers.traefik.service=api@internal
#       - traefik.http.routers.traefik.tls.certresolver=le

#   mariadb:
#     image: mariadb:11
#     container_name: mariadb
#     environment:
#       MARIADB_ROOT_PASSWORD: ${MARIADB_ROOT_PASSWORD}
#       MARIADB_DATABASE: ${MARIADB_NEXTCLOUD_DB}
#       MARIADB_USER: ${MARIADB_NEXTCLOUD_USER}
#       MARIADB_PASSWORD: ${MARIADB_NEXTCLOUD_PASSWORD}
#     volumes:
#       - mariadb-data:/var/lib/mysql
#     networks:
#       - proxy
#     restart: unless-stopped

#   adminer:
#     image: adminer:latest
#     container_name: adminer
#     depends_on:
#       - mariadb
#     environment:
#       ADMINER_DEFAULT_SERVER: mariadb
#     networks:
#       - proxy
#     restart: unless-stopped
#     labels:
#       - traefik.enable=true
#       - traefik.http.routers.dbadmin.rule=Host(`${DBADMIN_HOST}`)
#       - traefik.http.routers.dbadmin.entrypoints=websecure
#       - traefik.http.routers.dbadmin.tls.certresolver=le
#       - traefik.http.services.dbadmin.loadbalancer.server.port=8080

#   portainer:
#     image: portainer/portainer-ce:latest
#     container_name: portainer
#     volumes:
#       - /var/run/docker.sock:/var/run/docker.sock
#       - portainer-data:/data
#     networks:
#       - proxy
#     restart: unless-stopped
#     labels:
#       - traefik.enable=true
#       - traefik.http.routers.portainer.rule=Host(`${PORTAINER_HOST}`)
#       - traefik.http.routers.portainer.entrypoints=websecure
#       - traefik.http.routers.portainer.tls.certresolver=le
#       - traefik.http.services.portainer.loadbalancer.server.port=9000

#   # ---------- ELK ----------
#   elasticsearch:
#     image: docker.elastic.co/elasticsearch/elasticsearch:${ELASTIC_VERSION}
#     container_name: elasticsearch
#     environment:
#       - discovery.type=single-node
#       - xpack.security.enabled=false
#       - ES_JAVA_OPTS=-Xms1g -Xmx1g
#     ulimits:
#       memlock:
#         soft: -1
#         hard: -1
#     volumes:
#       - elastic-data:/usr/share/elasticsearch/data
#     networks:
#       - proxy
#     restart: unless-stopped

#   logstash:
#     image: docker.elastic.co/logstash/logstash:${ELASTIC_VERSION}
#     container_name: logstash
#     environment:
#       - xpack.monitoring.enabled=false
#     depends_on:
#       - elasticsearch
#     volumes:
#       - logstash-pipeline:/usr/share/logstash/pipeline
#     networks:
#       - proxy
#     restart: unless-stopped
#     # Crea un pipeline simple en /usr/share/logstash/pipeline/logstash.conf si quieres

#   kibana:
#     image: docker.elastic.co/kibana/kibana:${ELASTIC_VERSION}
#     container_name: kibana
#     depends_on:
#       - elasticsearch
#     environment:
#       - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
#     networks:
#       - proxy
#     restart: unless-stopped
#     labels:
#       - traefik.enable=true
#       - traefik.http.routers.kibana.rule=Host(`${KIBANA_HOST}`)
#       - traefik.http.routers.kibana.entrypoints=websecure
#       - traefik.http.routers.kibana.tls.certresolver=le
#       - traefik.http.services.kibana.loadbalancer.server.port=5601

#   # ---------- Nextcloud + Redis ----------
#   redis:
#     image: redis:alpine
#     container_name: redis
#     command: ["redis-server", "--appendonly", "yes"]
#     volumes:
#       - redis-data:/data
#     networks:
#       - proxy
#     restart: unless-stopped

#   nextcloud:
#     image: nextcloud:apache
#     container_name: nextcloud
#     depends_on:
#       - mariadb
#       - redis
#     environment:
#       # DB
#       MYSQL_HOST: mariadb
#       MYSQL_DATABASE: ${MARIADB_NEXTCLOUD_DB}
#       MYSQL_USER: ${MARIADB_NEXTCLOUD_USER}
#       MYSQL_PASSWORD: ${MARIADB_NEXTCLOUD_PASSWORD}
#       # Redis
#       REDIS_HOST: redis
#       # Reverse proxy / https
#       TRUSTED_PROXIES: **********/12,**********/16,10.0.0.0/8,***********/16
#       OVERWRITEPROTOCOL: https
#       OVERWRITECLIURL: https://${NEXTCLOUD_HOST}
#       OVERWRITEHOST: ${NEXTCLOUD_HOST}
#     volumes:
#       - nextcloud-data:/var/www/html
#       - nextcloud-apps:/var/www/html/custom_apps
#       - nextcloud-config:/var/www/html/config
#     networks:
#       - proxy
#     restart: unless-stopped
#     labels:
#       - traefik.enable=true
#       - traefik.http.routers.nextcloud.rule=Host(`${NEXTCLOUD_HOST}`)
#       - traefik.http.routers.nextcloud.entrypoints=websecure
#       - traefik.http.routers.nextcloud.tls.certresolver=le
#       - traefik.http.services.nextcloud.loadbalancer.server.port=80

#   # ---------- Keycloak (usando MariaDB) ----------
#   keycloak:
#     image: quay.io/keycloak/keycloak:24.0.5
#     container_name: keycloak
#     command: ["start", "--proxy=edge", "--hostname=${KEYCLOAK_HOST}"]
#     depends_on:
#       - mariadb
#     environment:
#       KC_HTTP_ENABLED: "true"
#       KC_PROXY: edge
#       KC_HOSTNAME: ${KEYCLOAK_HOST}
#       KC_HOSTNAME_STRICT: "false"
#       KC_HOSTNAME_STRICT_HTTPS: "false"
#       KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN}
#       KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
#       # DB (MariaDB/MySQL)
#       KC_DB: mysql
#       KC_DB_URL_HOST: mariadb
#       KC_DB_URL_DATABASE: ${MARIADB_KEYCLOAK_DB}
#       KC_DB_USERNAME: ${MARIADB_KEYCLOAK_USER}
#       KC_DB_PASSWORD: ${MARIADB_KEYCLOAK_PASSWORD}
#     networks:
#       - proxy
#     restart: unless-stopped
#     labels:
#       - traefik.enable=true
#       - traefik.http.routers.keycloak.rule=Host(`${KEYCLOAK_HOST}`)
#       - traefik.http.routers.keycloak.entrypoints=websecure
#       - traefik.http.routers.keycloak.tls.certresolver=le
#       - traefik.http.services.keycloak.loadbalancer.server.port=8080
