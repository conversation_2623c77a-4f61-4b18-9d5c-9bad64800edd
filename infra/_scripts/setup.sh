#!/bin/bash

echo "🚀 Configurando entorno Traefik + Cerebro Stack para MSarkNet"
echo "============================================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Función para imprimir mensajes con colores
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_cerebro() {
    echo -e "${PURPLE}🧠 $1${NC}"
}

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    print_error "Docker no está instalado. Por favor, instala Docker primero."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose no está instalado. Por favor, instala Docker Compose primero."
    exit 1
fi

print_status "Docker verificado"

# Verificar estructura de directorios
print_info "Verificando estructura de proyecto..."

# Directorios esperados
EXPECTED_DIRS=(
    "../cerebro-fe"
    "../cerebro-be"
)

MISSING_DIRS=()

for dir in "${EXPECTED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        MISSING_DIRS+=("$dir")
    fi
done

if [ ${#MISSING_DIRS[@]} -ne 0 ]; then
    print_warning "Faltan los siguientes directorios del stack Cerebro:"
    for dir in "${MISSING_DIRS[@]}"; do
        echo -e "${YELLOW}  - $dir${NC}"
    done
    echo ""
    print_info "Estructura esperada:"
    echo -e "${CYAN}proyecto-root/"
    echo -e "├── cerebro-fe/     # React Frontend"
    echo -e "├── cerebro-be/     # Node.js Backend"
    echo -e "└── infra/          # Docker setup (aquí estás)${NC}"
    echo ""

    read -p "¿Quieres continuar sin el stack Cerebro? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Setup cancelado. Crea los directorios necesarios y vuelve a ejecutar."
        exit 1
    fi

    SKIP_CEREBRO=true
else
    print_status "Estructura de directorios verificada"
    SKIP_CEREBRO=false
fi

# Crear estructura de directorios para infra
print_info "Creando estructura de directorios de infraestructura..."
mkdir -p certs
mkdir -p dynamic
mkdir -p web-content/main
mkdir -p web-content/portainer
mkdir -p web-content/docs
mkdir -p letsencrypt
mkdir -p backups

print_status "Directorios de infraestructura creados"

# Verificar Dockerfiles si existen los directorios
if [ "$SKIP_CEREBRO" = false ]; then
    print_cerebro "Verificando Dockerfiles del stack Cerebro..."

    if [ ! -f "../cerebro-fe/Dockerfile" ]; then
        print_warning "Falta Dockerfile en cerebro-fe"
        print_info "Crea el Dockerfile usando el template proporcionado"
    fi

    if [ ! -f "../cerebro-be/Dockerfile" ]; then
        print_warning "Falta Dockerfile en cerebro-be"
        print_info "Crea el Dockerfile usando el template proporcionado"
    fi

    if [ ! -f "../cerebro-fe/nginx.conf" ]; then
        print_warning "Falta nginx.conf en cerebro-fe para configuración SPA"
        print_info "Crea el nginx.conf usando el template proporcionado"
    fi
fi

# Verificar archivo /etc/hosts
print_info "Verificando configuración de /etc/hosts..."

HOSTS_ENTRIES=(
    "127.0.0.1 msarknet.me"
    "127.0.0.1 whoami.msarknet.me"
    "127.0.0.1 grafana.msarknet.me"
    "127.0.0.1 loki.msarknet.me"
    "127.0.0.1 prom.msarknet.me"
    "127.0.0.1 traefik.msarknet.me"
    "127.0.0.1 portainer.msarknet.me"
    "127.0.0.1 docs.msarknet.me"
    "127.0.0.1 app.msarknet.me"
    "127.0.0.1 api.msarknet.me"
    "127.0.0.1 db.msarknet.me"
)

MISSING_HOSTS=()

for entry in "${HOSTS_ENTRIES[@]}"; do
    if ! grep -q "$entry" /etc/hosts 2>/dev/null; then
        MISSING_HOSTS+=("$entry")
    fi
done

if [ ${#MISSING_HOSTS[@]} -ne 0 ]; then
    print_warning "Faltan entradas en /etc/hosts. Añade las siguientes líneas:"
    echo ""
    for entry in "${MISSING_HOSTS[@]}"; do
        echo -e "${YELLOW}$entry${NC}"
    done
    echo ""
    print_info "Para añadirlas automáticamente, ejecuta:"
    echo -e "${BLUE}sudo bash -c 'cat >> /etc/hosts << EOF"
    for entry in "${MISSING_HOSTS[@]}"; do
        echo "$entry"
    done
    echo "EOF'"
    echo -e "${NC}"

    read -p "¿Quieres que las añada automáticamente? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Añadiendo entradas a /etc/hosts..."
        for entry in "${MISSING_HOSTS[@]}"; do
            echo "$entry" | sudo tee -a /etc/hosts > /dev/null
        done
        print_status "Entradas añadidas a /etc/hosts"
    fi
else
    print_status "Configuración de /etc/hosts verificada"
fi

# Generar certificados SSL
print_info "Generando certificados SSL..."
if [ -f "./_scripts/generate-certs.sh" ]; then
    chmod +x ./_scripts/generate-certs.sh
    bash ./_scripts/generate-certs.sh
else
    print_error "No se encontró ./_scripts/generate-certs.sh"
    exit 1
fi

# Crear red Docker si no existe
print_info "Creando red Docker 'proxy'..."
docker network create proxy 2>/dev/null && print_status "Red Docker 'proxy' creada" || print_status "Red Docker 'proxy' ya existe"

# Verificar archivos necesarios
FILES_TO_CHECK=(
    "docker-compose.yml"
    "dynamic/tls.yml"
    "dynamic/middlewares.yml"
)

print_info "Verificando archivos de configuración..."
for file in "${FILES_TO_CHECK[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Archivo faltante: $file"
        exit 1
    fi
done
print_status "Archivos de configuración verificados"

# Crear archivos de contenido web si no existen
print_info "Creando contenido web de ejemplo..."

if [ ! -f "web-content/main/index.html" ]; then
    cat > web-content/main/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MSarkNet - Main App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; text-align: center; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 40px 0; }
        .service { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; }
        .service a { color: #fff; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MSarkNet Development Environment</h1>
        <p>Entorno de desarrollo con Traefik + Docker</p>

        <div class="services">
            <div class="service">
                <h3>🧠 Cerebro Frontend</h3>
                <p><a href="https://app.msarknet.me">app.msarknet.me</a></p>
            </div>
            <div class="service">
                <h3>⚙️ Cerebro API</h3>
                <p><a href="https://api.msarknet.me">api.msarknet.me</a></p>
            </div>
            <div class="service">
                <h3>📊 Dashboard</h3>
                <p><a href="https://traefik.msarknet.me">traefik.msarknet.me</a></p>
            </div>
            <div class="service">
                <h3>📚 Docs</h3>
                <p><a href="https://docs.msarknet.me">docs.msarknet.me</a></p>
            </div>
        </div>

        <p><small>Powered by Traefik + Docker</small></p>
    </div>
</body>
</html>
EOF
fi

# Crear contenido para otros servicios
for service in "portainer" "docs"; do
    if [ ! -f "web-content/$service/index.html" ]; then
        cat > "web-content/$service/index.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MSarkNet - $(echo $service | sed 's/.*/\u&/')</title>
    <style>body { font-family: Arial, sans-serif; margin: 50px; text-align: center; }</style>
</head>
<body>
    <h1>🚀 $(echo $service | sed 's/.*/\u&/') Service</h1>
    <p>Mock service running on Docker</p>
    <p><a href="https://msarknet.me">← Volver al inicio</a></p>
</body>
</html>
EOF
    fi
done

# Decidir qué servicios iniciar
if [ "$SKIP_CEREBRO" = true ]; then
    print_warning "Iniciando servicios básicos (sin stack Cerebro)..."
    SERVICES_TO_START="traefik main-app grafana-mock prometheus-mock portainer-mock docs api-service"
else
    print_cerebro "Iniciando todos los servicios incluyendo stack Cerebro..."
    SERVICES_TO_START=""  # Vacío = todos los servicios
fi

# Iniciar servicios
print_info "Iniciando servicios con Docker Compose..."
if [ -n "$SERVICES_TO_START" ]; then
    docker compose up -d $SERVICES_TO_START
else
    docker compose up -d
fi

# Esperar a que los servicios estén listos
print_info "Esperando a que los servicios estén listos..."
sleep 15

# Verificar estado de los servicios
print_info "Verificando estado de los servicios..."
if docker compose ps | grep -q "Up"; then
    print_status "Servicios iniciados correctamente"
else
    print_error "Algunos servicios no se iniciaron correctamente"
    docker compose ps
    exit 1
fi

# Mostrar información de acceso
echo ""
echo "🎉 ¡Configuración completada!"
echo "=============================="
echo ""

if [ "$SKIP_CEREBRO" = false ]; then
    print_cerebro "🧠 Stack Cerebro disponible:"
    echo -e "🎯 Frontend React:          ${BLUE}https://app.msarknet.me${NC}"
    echo -e "⚡ Backend Node.js:         ${BLUE}https://api.msarknet.me${NC}"
    echo -e "🗄️  MongoDB:                ${BLUE}https://db.msarknet.me${NC} (protegido)"
    echo ""
fi

print_info "📋 Servicios originales disponibles:"
echo -e "🌐 Aplicación principal:    ${BLUE}https://msarknet.me${NC}"
echo -e "📊 Grafana (mock):          ${BLUE}https://grafana.msarknet.me${NC}"
echo -e "📈 Prometheus (mock):       ${BLUE}https://prom.msarknet.me${NC}"
echo -e "⚙️  Dashboard Traefik:       ${BLUE}https://traefik.msarknet.me${NC}"
echo -e "🐳 Portainer (mock):        ${BLUE}https://portainer.msarknet.me${NC}"
echo -e "📚 Documentación:           ${BLUE}https://docs.msarknet.me${NC}"
echo -e "🔧 API Backend:             ${BLUE}https://msarknet.me/api${NC}"
echo ""

print_warning "Los certificados son autofirmados. Acepta la advertencia de seguridad en el navegador."
echo ""

print_info "🛠️  Comandos útiles:"
echo -e "${BLUE}docker compose ps${NC}                     # Ver estado de servicios"
echo -e "${BLUE}docker compose logs -f${NC}               # Ver todos los logs"
echo -e "${BLUE}docker compose logs -f cerebro-fe${NC}    # Ver logs del frontend"
echo -e "${BLUE}docker compose logs -f cerebro-be${NC}    # Ver logs del backend"
echo -e "${BLUE}docker compose restart [servicio]${NC}    # Reiniciar un servicio"
echo -e "${BLUE}docker compose down${NC}                  # Parar todos los servicios"
echo -e "${BLUE}make -f docker.mk help${NC}               # Ver comandos avanzados"
echo ""

# Test básico de conectividad
print_info "Realizando tests de conectividad..."

# Test servicios básicos
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ | grep -q "200\|301\|302"; then
    print_status "✅ Main app OK"
else
    print_warning "⚠️  Main app test failed"
fi

# Test Dashboard Traefik
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/rawdata | grep -q "200"; then
    print_status "✅ Traefik Dashboard OK"
else
    print_warning "⚠️  Traefik Dashboard test failed"
fi

# Test stack Cerebro si está disponible
if [ "$SKIP_CEREBRO" = false ]; then
    sleep 5  # Esperar un poco más para servicios complejos

    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ -H "Host: app.msarknet.me" | grep -q "200\|301\|302"; then
        print_status "✅ Cerebro Frontend OK"
    else
        print_warning "⚠️  Cerebro Frontend test failed (puede estar construyendo...)"
    fi

    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ -H "Host: api.msarknet.me" | grep -q "200\|301\|302"; then
        print_status "✅ Cerebro Backend OK"
    else
        print_warning "⚠️  Cerebro Backend test failed (puede estar iniciando...)"
    fi
fi

echo ""
print_status "🎊 ¡Todo listo para desarrollo!"

if [ "$SKIP_CEREBRO" = false ]; then
    print_cerebro "💡 Tips para desarrollo con Cerebro:"
    echo -e "   • El frontend React tiene ${GREEN}hot reload${NC} activado"
    echo -e "   • El backend Node.js usa ${GREEN}nodemon${NC} para auto-restart"
    echo -e "   • MongoDB está disponible internamente en cerebro-db:27017"
    echo -e "   • Los logs se pueden ver con: ${BLUE}docker compose logs -f cerebro-fe cerebro-be${NC}"
fi
